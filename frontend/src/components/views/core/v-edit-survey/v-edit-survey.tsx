import { Component, Prop, State, h } from '@stencil/core';
import { GetSurveyApi } from '../../../../global/script/helpers';
import { TabItem } from '../../../patterns/p-tabnav/types';
import { FrontendLogger } from '../../../../global/script/var';

@Component({
  tag: 'v-edit-survey',
  styleUrl: 'v-edit-survey.css',
  shadow: true,
})
export class VEditSurvey {
  @Prop() surveyId: string;

  @State() isLoading: boolean = true;
  @State() loadError: boolean = false;
  @State() errorMessage: string = '';
  @State() survey: any = null;
  @State() activeTab: string = 'basic';

  private getTabs(): TabItem[] {
    const surveyTypeName = this.survey?.type ? this.formatSurveyType(this.survey.type) : 'Survey';
    return [
      { id: 'basic', label: 'Basic Settings' },
      { id: 'type', label: `${surveyTypeName} Settings` },
      { id: 'respondent', label: 'Respondent Settings' },
    ];
  }

  private formatSurveyType(type: string): string {
    return type.charAt(0).toUpperCase() + type.slice(1);
  }

  componentWillLoad() {
    document.title = 'Edit Survey | Sensefolks';
    this.fetchSurveyData();
  }

  /**
   * Fetches survey data from the API
   * Updates component state based on the result
   */
  async fetchSurveyData() {
    // DEBUG: Log survey data fetch attempt
    FrontendLogger.debug('Fetching survey data for editing', {
      surveyId: this.surveyId,
    });

    this.isLoading = true;
    this.loadError = false;
    this.errorMessage = '';

    try {
      const { success, message, payload } = await GetSurveyApi(this.surveyId);
      if (!success || !payload) {
        this.loadError = true;
        this.errorMessage = message || 'Failed to load survey data';
        FrontendLogger.warn('Survey data fetch failed', {
          surveyId: this.surveyId,
          success,
          message,
          hasPayload: !!payload,
        });
        return;
      }

      this.survey = payload;
      FrontendLogger.debug('Survey data fetched successfully', {
        surveyId: this.surveyId,
        surveyTitle: payload.title,
      });
    } catch (error) {
      this.loadError = true;
      this.errorMessage = 'An error occurred while fetching survey data';

      // DEBUG: Log survey data fetch error
      FrontendLogger.error('Error occurred while fetching survey data', {
        surveyId: this.surveyId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined,
      });
    } finally {
      this.isLoading = false;
    }
  }

  handleTabChange(tabId: string) {
    this.activeTab = tabId;
  }

  generateSurveyPill(value: string) {
    if (value === 'sensePrice') {
      return (
        <e-text variant="footnote">
          <e-pill color="purple">SensePrice</e-pill>
        </e-text>
      );
    } else if (value === 'senseFeature') {
      return (
        <e-text variant="footnote">
          <e-pill color="blue">SenseFeature</e-pill>
        </e-text>
      );
    } else if (value === 'sensePoll') {
      return (
        <e-text variant="footnote">
          <e-pill color="indigo">SensePoll</e-pill>
        </e-text>
      );
    } else if (value === 'senseQuery') {
      return (
        <e-text variant="footnote">
          <e-pill color="turquoise">SenseQuery</e-pill>
        </e-text>
      );
    }
    return null;
  }

  renderLoading() {
    return (
      <div class="container container__spinner">
        <e-spinner theme="dark"></e-spinner>
      </div>
    );
  }

  renderError() {
    return (
      <div class="error-container">
        <article>
          <e-text variant="display">Could not load survey details</e-text>
          <l-spacer value={1}></l-spacer>
          <e-text>{this.errorMessage}</e-text>
          <l-spacer value={2}></l-spacer>
          <e-link url="/">← Back to Home</e-link>
        </article>
      </div>
    );
  }

  renderTabContent() {
    switch (this.activeTab) {
      case 'basic':
        return (
          <basic-settings-section
            surveyId={this.surveyId}
            survey={this.survey}
          ></basic-settings-section>
        );
      case 'type':
        return (
          <survey-type-settings-section
            surveyId={this.surveyId}
            survey={this.survey}
          ></survey-type-settings-section>
        );
      case 'respondent':
        return (
          <respondent-settings-section
            surveyId={this.surveyId}
            survey={this.survey}
          ></respondent-settings-section>
        );
      default:
        return (
          <basic-settings-section
            surveyId={this.surveyId}
            survey={this.survey}
          ></basic-settings-section>
        );
    }
  }

  renderSurveyEditor() {
    return (
      <div class="container">
        <l-row>
          <e-link url={`/surveys/${this.surveyId}/results`}>← Back</e-link>
          <div></div>
        </l-row>
        <l-spacer value={2}></l-spacer>
        <e-text variant="display">Edit Survey</e-text>
        <l-spacer value={1}></l-spacer>
        {/* <e-text variant="footnote">SURVEY TYPE (Cannot be edited)</e-text>
        <l-spacer value={0.25}></l-spacer>
        {this.generateSurveyPill(this.survey.type)} */}
        <l-row justifyContent="flex-start">
          {this.generateSurveyPill(this.survey.type)}{' '}
          <l-spacer variant="horizontal" value={0.25}></l-spacer>
          <e-text variant="footnote">(Survey type cannot be edited)</e-text>
        </l-row>
        <l-spacer value={2}></l-spacer>
        {/* Tab Navigation */}
        <p-tabnav
          tabs={this.getTabs()}
          activeTab={this.activeTab}
          onTabChange={e => this.handleTabChange(e.detail)}
        ></p-tabnav>
        {/* Tab Content */}
        <div class="tab-content">{this.renderTabContent()}</div>
      </div>
    );
  }

  render() {
    if (this.isLoading) {
      return <c-main>{this.renderLoading()}</c-main>;
    }

    if (this.loadError) {
      return <c-main>{this.renderError()}</c-main>;
    }

    return <c-main>{this.renderSurveyEditor()}</c-main>;
  }
}
