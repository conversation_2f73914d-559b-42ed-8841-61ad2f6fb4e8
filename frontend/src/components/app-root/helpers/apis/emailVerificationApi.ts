import { emailVerificationPayloadInterface } from '../../interfaces';
import { Var, FrontendLogger } from '../../../../global/script/var';
import { ConstructApiUrl } from '../../../../global/script/helpers';

export const emailVerificationApi = async (payload: emailVerificationPayloadInterface) => {
  let url: string = ConstructApiUrl(Var.api.endpoint.account.email.verify);
  let options: any = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify(payload),
  };

  let returnData: any;
  await fetch(url, options)
    .then(response => response.json())
    .then(data => {
      returnData = data;
    })
    .catch(error => {
      // DEBUG: Log email verification error
      FrontendLogger.error('Email verification API error', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined,
      });
    });

  return {
    success: returnData.success,
    message: returnData.message,
    payload: returnData.payload,
  };
};
