import { Component, Prop, State, Event, EventEmitter, Listen, h } from '@stencil/core';

/**
 * Global modal component that can be used from any part of the webapp
 */
@Component({
  tag: 'p-modal',
  styleUrl: 'p-modal.css',
  shadow: true,
})
export class PModal {
  @Prop() isOpen: boolean = false;
  @Prop() modalTitle: string = '';
  @Prop() closeOnBackdropClick: boolean = true;

  @State() isVisible: boolean = false;

  @Event({
    eventName: 'modalCloseEvent',
    bubbles: true,
  })
  modalCloseEventEmitter: EventEmitter;

  @Listen('keydown', { target: 'window' })
  handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Escape' && this.isVisible) {
      this.closeModal();
    }
  }

  componentWillLoad() {
    this.isVisible = this.isOpen;
  }

  componentWillUpdate() {
    if (this.isOpen !== this.isVisible) {
      this.isVisible = this.isOpen;

      if (this.isVisible) {
        document.body.style.overflow = 'hidden'; // Prevent scrolling when modal is open
      } else {
        document.body.style.overflow = ''; // Restore scrolling when modal is closed
      }
    }
  }

  disconnectedCallback() {
    // Ensure scrolling is restored when component is removed
    document.body.style.overflow = '';
  }

  closeModal() {
    this.isVisible = false;
    this.modalCloseEventEmitter.emit();
  }

  handleBackdropClick(event: MouseEvent) {
    // Only close if clicking directly on the backdrop, not on modal content
    if (
      this.closeOnBackdropClick &&
      (event.target as HTMLElement).classList.contains('modal-backdrop')
    ) {
      this.closeModal();
    }
  }

  render() {
    if (!this.isVisible) {
      return null;
    }

    return (
      <div class="modal-backdrop" onClick={e => this.handleBackdropClick(e)}>
        <div class="modal-container">
          <div class="modal-header">
            <e-text variant="heading">{this.modalTitle}</e-text>
            <e-button variant="link" onClick={() => this.closeModal()}>
              <e-image src="../../../assets/icon/red/x-red.svg" width="1.25em"></e-image>
            </e-button>
          </div>
          <l-spacer value={1}></l-spacer>
          <l-separator></l-separator>
          <l-spacer value={1}></l-spacer>
          <div class="modal-content">
            <slot></slot>
          </div>
          <l-spacer value={3}></l-spacer>
          <div class="modal-footer">
            <slot name="footer"></slot>
          </div>
        </div>
      </div>
    );
  }
}
